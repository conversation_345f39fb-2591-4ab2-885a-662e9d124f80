<script lang="ts">
	import type { Jasa, Paket, Sparepart } from '$lib/schema/general';
	import {
		getInvoiceState,
		type CustomService,
		type Service
	} from '../../serviceOrder/InvoiceState.svelte';

	import ChooseService from '../ChooseService.svelte';
	import JasaModal from './utility/JasaModal.svelte';
	import SparepartModal from './utility/SparepartModal.svelte';
	import PaketModal from './utility/PaketModal.svelte';
	import Icon from '@iconify/svelte';

	interface IProps {
		body: Service[];
	}
	const { body }: IProps = $props();
	const kind = $derived(body[0]?.kind);

	const invoiceState = getInvoiceState();
</script>

{#if !body || body.length === 0}
	<ChooseService />
{:else if kind === 'paket'}
	<div class="text-primary flex flex-col gap-1">
		{#each body as b}
			{@const service = b.data as Paket}
			<div class="flex flex-col justify-between gap-2">
				<h3 class="font-semibold">{service.nama_paket}</h3>

				<ol class="list-inside">
					{#each service.jasa_paket as detail}
						<li class="flex items-center gap-2">
							<Icon icon="mdi:wrench" />
							{detail.nama_jasa}
						</li>
					{/each}
				</ol>

				<ol class="list-inside list-decimal">
					{#each service.sparepart_paket as detail}
						<li class="flex items-center gap-2">
							<Icon icon="mdi:car-battery" />
							{detail.nama_sparepart}
						</li>
					{/each}
				</ol>
			</div>
		{/each}

		{#if invoiceState.editable}
			<PaketModal onTable />
		{/if}
	</div>
{:else if kind === 'jasa'}
	<div class="text-primary flex flex-col gap-1">
		<h3 class="mb-1 font-semibold">Jasa</h3>

		<ol class="flex list-inside list-decimal flex-col gap-1 ps-1">
			{#each body as b}
				{@const jasa = b.data as Jasa}
				<li class="mb-1">{jasa.nama_jasa}</li>
			{/each}
		</ol>

		{#if invoiceState.editable}
			<JasaModal onTable />
		{/if}
	</div>

	{#if invoiceState.editable}
		<br />
	{/if}
{:else if kind === 'sparepart'}
	<div class="text-primary flex flex-col gap-1">
		<h3 class="mb-1 font-semibold">Sparepart</h3>

		<ol class="flex list-inside list-decimal flex-col gap-1 ps-1">
			{#each body as b}
				{@const sparepart = b.data as Sparepart}
				<li class="mb-1">{sparepart.nama_sparepart}</li>
			{/each}
		</ol>

		{#if invoiceState.editable}
			<SparepartModal onTable />
		{/if}
	</div>

	{#if invoiceState.editable}
		<br />
	{/if}
{:else if kind === 'custom'}
	<div class="text-primary flex flex-col gap-1">
		<h3 class="mb-1 font-semibold">Custom Service</h3>

		<ol class="flex list-inside list-decimal flex-col gap-1 ps-1">
			{#each body as b}
				{@const custom = b.data as CustomService}
				<li class="mb-1">{custom.nama}</li>
			{/each}
		</ol>

		{#if invoiceState.editable}
			<ChooseService mode="add" title="Tambah Manual"></ChooseService>
		{/if}
	</div>
{:else}
	<div>{kind}</div>
{/if}
