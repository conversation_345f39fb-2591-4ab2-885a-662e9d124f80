import type { Order } from '$lib/schema/order';

import { isActionFailure } from '@sveltejs/kit';
import { form, query } from '$app/server';

import { Effect } from 'effect';

import { launch_fetch, retrieve_fetch } from '$lib/utils/fetch';

export const getWaitingList = query(async () => {
	const getOrder = retrieve_fetch<Order[]>('/order/status/Antrian');
	const response = await Effect.runPromise(getOrder);
	return response.data;
});

export const workOnOrder = form(async (data) => {
	const order: Order = JSON.parse(data.get('order') as string);

	const action = launch_fetch(`/order`, {
		method: 'PUT',
		body: JSON.stringify({
			...order,
			status_order: 'Dikerjakan',
			id_bengkel: order.bengkel.id_bengkel,
			id_karyawan: order.karyawan.id_karyawan,
			id_customer: order.customer.id_customer,
			nomor_polisi: order.kendaraan.nomor_polisi
		})
	});

	const response = await Effect.runPromise(action);

	if (isActionFailure(response)) return { success: false };

	return { ...response, success: true };
});

export const deleteOrder = form(async (data) => {
	const nomor_order = data.get('id') as string;

	const action = launch_fetch(`/order/${nomor_order}`, {
		method: 'DELETE'
	});

	const response = await Effect.runPromise(action);

	if (isActionFailure(response)) return { success: false };

	return { ...response, success: true };
});
