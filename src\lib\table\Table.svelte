<script lang="ts" generics="T extends Record<string,any>, U extends string">
	import type { Snippet } from 'svelte';
	import type { TableHeader } from './types';

	import { transform } from './util';
	import { page } from '$app/state';

	const limit = $derived(Number(page.url.searchParams.get('limit') ?? 10) ?? 10);
	const offset = $derived(Number(page.url.searchParams.get('offset') ?? 0) ?? 0);

	interface Props {
		table_data: T[] | undefined;
		table_header: TableHeader<T, U>;

		custom?: Snippet<[{ header: U; body: T; index: number }]>;

		config?: { checkbox: boolean };
		checked?: Record<string, any>[];
	}

	let {
		table_data = [],
		table_header = [],

		custom,

		config = { checkbox: false },
		checked = $bindable([])
	}: Props = $props();
</script>

<div class="w-full max-w-[calc(100vw-2rem)] overflow-auto">
	<table>
		<thead>
			<tr class="text-base-content border-base-content/10 bg-secondary">
				{#if config.checkbox}
					<th class="w-3 leading-none">
						<input type="checkbox" class="checkbox checkbox-sm checkbox-accent" />
					</th>
				{/if}
				{#each table_header as th}
					<th
						class:justify-center={th instanceof Array &&
							(th[0] === 'numbering' || th[2] === 'center')}
						class:text-left={th instanceof Array && th[0] !== 'numbering' && th[2] !== 'center'}
					>
						{th instanceof Array ? th[1] : th}
					</th>
				{/each}
			</tr>
		</thead>

		<tbody>
			{#each table_data ?? [] as td, index (index)}
				<tr class="border-base-content/10">
					{#if config.checkbox}
						<td class="w-3 rounded text-center leading-none">
							<input
								type="checkbox"
								bind:group={checked}
								class="checkbox checkbox-sm checkbox-accent"
								value={td}
							/>
						</td>
					{/if}

					{#each table_header as th}
						{#if td[th as keyof T]}
							<td>{td[th as keyof T]}</td>
						{:else if th instanceof Array}
							{#if th[0] !== 'custom' && th[2]}
								<td>{transform(td[th[0]], th[2])}</td>
							{:else if th[0] === 'custom'}
								<td>
									<div class="w-full">
										{@render custom?.({ header: th[1], body: td, index: index })}
									</div>
								</td>
							{:else if th[0] === 'numbering'}
								<td class="text-center !text-lg font-bold tabular-nums">
									{index + 1 + offset}
								</td>
							{:else}
								<td>{td[th[0]]}</td>
							{/if}
						{/if}
					{/each}
				</tr>
			{:else}
				<tr class="border-base-100/10">
					<td colspan="100">there is no data.</td>
				</tr>
			{/each}
		</tbody>
	</table>
</div>

<style>
	@import 'tailwindcss';

	table {
		@apply w-full table-auto border-collapse;
	}

	tr {
		@apply rounded border;
	}

	tr:nth-child(even) {
		@apply bg-blue-50;
	}

	td,
	th {
		@apply px-3 py-2 text-sm;
	}

	th {
		@apply py-2;
	}
</style>
