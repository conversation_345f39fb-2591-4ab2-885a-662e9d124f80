<script lang="ts">
	import { _Kendaraan, type Kendaraan } from '$lib/schema/general';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
</script>

<div class="join">
	<button
		class="btn btn-sm btn-outline btn-primary join-item"
		class:btn-active={invoiceState.vehicleMode === 'terdaftar'}
		onclick={() => {
			invoiceState.vehicleMode = 'terdaftar';
			invoiceState.order.kendaraan = _Kendaraan;
			invoiceState.order.nomor_polisi = _Kendaraan.nomor_polisi;
			utilityModalState.modal?.showModal();
		}}
	>
		<Icon icon="mdi:car-search" /> Terdaftar
	</button>

	<button
		class="btn btn-sm btn-outline btn-primary join-item"
		class:btn-active={invoiceState.vehicleMode === 'baru'}
		onclick={() => {
			invoiceState.vehicleMode = 'baru';
			invoiceState.order.kendaraan = _Kendaraan;
			invoiceState.order.nomor_polisi = _Kendaraan.nomor_polisi;
		}}
	>
		<Icon icon="mdi:car-key" /> Baru
	</button>
</div>

<UtilityModal url="/kendaraan">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Kendaraan</h2>
	{/snippet}

	{#snippet item({ item }: { item: Kendaraan })}
		<button
			class="btn btn-outline mb-1 w-full"
			onclick={() => {
				invoiceState.order.kendaraan = item;
				invoiceState.order.nomor_polisi = item.nomor_polisi;

				utilityModalState.modal?.close();
			}}
		>
			{item.nama_kendaraan} ({item.nomor_polisi})
		</button>
	{/snippet}
</UtilityModal>
