<script lang="ts">
	import { goto } from '$app/navigation';
	import { shortcut } from '$lib/actions/shortcut.svelte';
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import TimeElapsed from './components/TimeElapsed.svelte';

	const { data } = $props();

	/**
	 * Format time to Jakarta timezone
	 * @param dateString - ISO date string
	 * @returns Formatted time string in Jakarta timezone
	 */
	function formatJakartaTime(dateString: string): string {
		const date = new Date(dateString);
		return date.toLocaleTimeString('id-ID', {
			timeZone: 'Asia/Jakarta',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: false
		});
	}

	$inspect(data);
</script>

<svelte:window
	use:shortcut={[
		{ key: 'escape', callback: () => goto('/order') },
		{ key: 'enter', callback: () => goto('/order/data') }
	]}
/>

<div class="flex justify-around gap-2">
	<a href="/order" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<a href="/order/data" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:plus" /> Tambah Order
		</button>
	</a>

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<Table table_header={data.tableHeader} table_data={data.list}>
	{#snippet custom({ header, body })}
		{#if header === 'Nama Pelanggan'}
			<div>{body.customer.nama}</div>
		{:else if header === 'Nomor Order'}
			<a href="/order/data/{body.nomor_order}" class="link link-primary">
				{body.nomor_order}
			</a>
		{:else if header === 'Jam Masuk'}
			{formatJakartaTime(body.created_at)}
		{:else if header === 'Waktu Berjalan'}
			<TimeElapsed start={body.updated_at || body.created_at} />
		{:else if header === 'Status'}
			{#if body.status === 'Antrian'}
				<div class="badge badge-primary text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Dikerjakan'}
				<div class="badge badge-warning text-[.75em] font-semibold uppercase">{body.status}</div>
			{:else if body.status === 'Selesai'}
				<div class="badge badge-success text-[.75em] font-semibold uppercase">{body.status}</div>
			{:else if body.status === 'Void'}
				<div class="badge badge-soft text-[.75em] font-semibold text-gray-600 uppercase">
					{body.status}
				</div>
			{/if}
		{:else if header === 'Actions'}
			<div class="flex items-center gap-2">
				{#if body.status === 'Antrian'}
					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:delete-bin-6-fill" />
						hapus
					</button>
				{:else if body.status === 'Dikerjakan'}
					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:pencil-fill" />
						edit
					</button>

					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:printer-fill" />
						cetak invoice
					</button>
				{:else if body.status === 'Selesai'}
					{#if body.nomor_invoice}
						<a href="/invoice/{body.nomor_invoice}">
							<button class="btn btn-sm btn-outline uppercase">
								<Icon icon="ri:external-link-line" />
								lihat invoice
							</button>
						</a>
					{/if}

					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:close-large-fill" />
						void
					</button>
				{/if}
			</div>
		{/if}
	{/snippet}
</Table>

<br />

<PaginationUsingParam total_content={data.total_rows} />
