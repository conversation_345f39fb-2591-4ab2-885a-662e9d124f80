<script lang="ts">
	import { untrack } from 'svelte';

	import ServiceOrderTable from './components/ServiceOrderTable.svelte';

	import logoCombined from '$lib/images/logo-combined.webp';
	import {
		getInvoiceState,
		setInvoiceState,
		type Service
	} from './serviceOrder/InvoiceState.svelte.js';

	import InvoiceConclusion from './components/InvoiceConclusion.svelte';
	import InvoiceFooter from './components/InvoiceFooter.svelte';
	import InvoiceEssentials from './components/InvoiceEssentials.svelte';

	const { data, params } = $props();

	let invoiceState = $state(getInvoiceState());
	setInvoiceState(data.invoice);

	$effect(() => {
		invoiceState = getInvoiceState();

		untrack(() => {
			if (params.order_id) {
				invoiceState.editable = false;
				invoiceState.order = data.invoice.order;
				invoiceState.ppn = data.invoice.order.pajak;
				invoiceState.discountByValue = data.invoice.order.diskon;

				type Services = [Service[], Service[], Service[], Service[]];
				invoiceState.service_order = data.service_order as Services;
			}
		});
	});
</script>

<main class="flex min-h-full flex-col gap-2">
	<InvoiceEssentials />

	<section
		class="invoice-background flex flex-col gap-4 border-2 border-gray-100"
		style="background-image: url({logoCombined}), url({logoCombined}), url({logoCombined});"
	>
		<section class="grow overflow-auto">
			<ServiceOrderTable />
		</section>

		<InvoiceConclusion />
	</section>

	<InvoiceFooter />
</main>

<style>
	.invoice-background {
		background-size: 150px;
		background-color: rgba(255, 255, 255, 0.95);
		background-blend-mode: lighten;
		background-position: 0 0;
		background-repeat: repeat;
	}
</style>
