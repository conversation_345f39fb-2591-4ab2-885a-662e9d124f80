import { Schema } from 'effect';
import {
	AlamatSchema,
	BengkelSchema,
	CustomerSchema,
	KaryawanSchema,
	JasaSchema,
	SparepartSchema,
	KendaraanSchema,
	_<PERSON><PERSON><PERSON>,
	_<PERSON><PERSON><PERSON>,
	_<PERSON><PERSON>,
	_<PERSON><PERSON><PERSON>,
	_Kendaraan,
	<PERSON>ir<PERSON>che<PERSON>,
	_<PERSON><PERSON>,
	_<PERSON><PERSON><PERSON><PERSON>,
	_<PERSON><PERSON>,
	PaketSchema,
	_<PERSON><PERSON>
} from './general';
import { jenisLayanan, metodePembayaran, orderStatus, statusTransaksi } from './literal';
import { MetadataSchema, NoPolisiSchema } from './basic';

export const OrderSchema = Schema.Struct({
	nomor_order: Schema.NullOr(Schema.NonEmptyString),
	nomor_invoice: Schema.NullOr(Schema.NonEmptyString),
	nomor_polisi: NoPolisiSchema,

	bengkel: BengkelSchema,

	karyawan: KaryawanSchema.omit('username', 'password'),
	customer: CustomerSchema.pipe(Schema.mutable),
	kendaraan: KendaraanSchema.pipe(Schema.mutable),

	pengantar: Schema.NonEmptyString.annotations({
		message: () => 'Nama Pengantar Tidak Boleh Kosong'
	}),
	jenis_layanan: Schema.Literal(...jenisLayanan),
	alamat: AlamatSchema,

	status: Schema.Literal(...orderStatus),

	keluhan: Schema.String,
	diagnosis: Schema.String,

	diskon: Schema.Number,
	pajak: Schema.Positive,
	metode_pembayaran: Schema.Literal(...metodePembayaran),
	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Order extends Schema.Schema.Type<typeof OrderSchema> {}
export interface OrderEncoded extends Schema.Schema.Encoded<typeof OrderSchema> {}

export const _Order: Order = {
	nomor_order: null,
	nomor_invoice: null,
	nomor_polisi: '',

	bengkel: _Bengkel,
	karyawan: _Karyawan,
	customer: _Customer,
	kendaraan: _Kendaraan,
	pengantar: '',
	jenis_layanan: 'Di Bengkel',
	alamat: _Alamat,
	status: 'Antrian',
	keluhan: '',
	diagnosis: '',
	diskon: 0,
	pajak: 11,
	metode_pembayaran: 'Cash',
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

//

export const OrderSparepart = Schema.Struct({
	id_order_sparepart: Schema.NullOr(Schema.Number),
	nomor_order: Schema.String,
	montir: MontirSchema.omit('username', 'password'),
	sparepart: SparepartSchema.omit('created_at', 'updated_at', 'deleted_at'),

	id_sparepart: Schema.String,
	nama_sparepart: Schema.String,
	harga: Schema.Number,
	kuantitas: Schema.Number,

	keterangan: Schema.String,

	...MetadataSchema.fields
});

export interface OrderSparepart extends Schema.Schema.Type<typeof OrderSparepart> {}
export interface OrderSparepartEncoded extends Schema.Schema.Encoded<typeof OrderSparepart> {}

export const _OrderSparepart: OrderSparepart = {
	id_order_sparepart: null,
	nomor_order: '',

	montir: _Montir,
	sparepart: _Sparepart,

	id_sparepart: '',
	nama_sparepart: '',
	harga: 0,
	kuantitas: 0,
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null
};

//

export const OrderJasa = Schema.Struct({
	id_order_jasa: Schema.NullOr(Schema.Number),
	nomor_order: Schema.String,

	montir: MontirSchema.omit('username', 'password'),
	jasa: JasaSchema.omit('created_at', 'updated_at', 'deleted_at'),

	id_jasa: Schema.String,
	nama_jasa: Schema.String,
	kuantitas: Schema.Number,
	harga: Schema.Number,

	keterangan: Schema.String,

	...MetadataSchema.fields
});

export interface OrderJasa extends Schema.Schema.Type<typeof OrderJasa> {}
export interface OrderJasaEncoded extends Schema.Schema.Encoded<typeof OrderJasa> {}

export const _OrderJasa: OrderJasa = {
	id_order_jasa: null,
	nomor_order: '',

	montir: _Montir,
	jasa: _Jasa,

	id_jasa: '',
	nama_jasa: '',
	kuantitas: 0,
	harga: 0,
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const OrderPaket = Schema.Struct({
	id_order_paket: Schema.NullOr(Schema.Number),
	nomor_order: Schema.String,

	montir: MontirSchema.omit('username', 'password'),
	paket: PaketSchema.omit('created_at', 'updated_at', 'deleted_at'),

	id_paket: Schema.String,
	nama_paket: Schema.String,
	jasa_paket: Schema.Array(JasaSchema),
	sparepart_paket: Schema.Array(SparepartSchema),

	kuantitas: Schema.Number,
	harga: Schema.Number,

	keterangan: Schema.String,
	...MetadataSchema.fields
});

export interface OrderPaket extends Schema.Schema.Type<typeof OrderPaket> {}
export interface OrderPaketEncoded extends Schema.Schema.Encoded<typeof OrderPaket> {}

export const _OrderPaket: OrderPaket = {
	id_order_paket: null,
	nomor_order: '',

	montir: _Montir,
	paket: _Paket,

	id_paket: '',
	nama_paket: '',
	jasa_paket: [],
	sparepart_paket: [],
	kuantitas: 0,
	harga: 0,
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null
};

//

export const InvoiceSchema = Schema.Struct({
	nomor_invoice: Schema.String,

	order: OrderSchema,
	karyawan: KaryawanSchema,

	subtotal: Schema.Positive,
	total: Schema.Positive,

	status_transaksi: Schema.Literal(...statusTransaksi),
	keterangan: Schema.String,

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),

	void_status: Schema.String,
	keterangan_void: Schema.String
});

export interface Invoice extends Schema.Schema.Type<typeof InvoiceSchema> {}
export interface InvoiceEncoded extends Schema.Schema.Encoded<typeof InvoiceSchema> {}

export const _Invoice: Invoice = {
	nomor_invoice: '',
	order: _Order,
	karyawan: _Karyawan,
	subtotal: 0,
	total: 0,
	status_transaksi: 'Belum Dibayar',
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null,
	void_status: '',
	keterangan_void: ''
};
