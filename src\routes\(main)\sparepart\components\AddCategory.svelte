<script lang="ts">
	import type { Mutable } from 'effect/Types';
	import Icon from '@iconify/svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import { _KategoriSparepart, type KategoriSparepart } from '$lib/schema/general';
	import { createCategory } from './category.remote';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	let modal = $state<HTMLDialogElement>();

	let kategori = $state<Mutable<KategoriSparepart>>(_KategoriSparepart);

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: 'Apakah Anda yakin akan menambahkan kategori ini?',
				loader: 'create:category'
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<button class="btn btn-outline btn-primary btn-xs my-1" onclick={() => modal?.showModal()}>
	<Icon icon="mdi:plus-thick" /> Tambah Kategori
</button>

<dialog class="modal" bind:this={modal}>
	<div class="modal-box text-primary">
		<div class="flex items-center justify-between">
			<h3 class="text-lg font-bold">Tambah Kategori Sparepart</h3>
			<button class="btn btn-sm btn-circle btn-ghost" onclick={() => modal?.close()}>✕</button>
		</div>

		<br />

		<form
			{...createCategory.enhance(async ({ submit, form }) => {
				await submit();

				if (createCategory.result?.success) {
					toastState.add({
						message: createCategory.result.message,
						type: 'success'
					});
					form.reset();
					modal?.close();
				} else {
					if (createCategory.result?.errors)
						validationErrorState.errors = createCategory.result.errors;

					toastState.add({
						message: createCategory.result?.message ?? '',
						type: 'error'
					});
				}

				confirmState.loader = '';
			})}
		>
			<FormField
				name="id_kategori_sparepart"
				label="Kode Kategori"
				type="text"
				bind:value={kategori.id_kategori_sparepart}
			/>

			<div class="my-2"></div>

			<FormField
				name="nama_kategori"
				label="Nama Kategori"
				type="text"
				bind:value={kategori.nama_kategori}
			/>

			<br />

			<input type="hidden" name="kategori_sparepart" value={JSON.stringify(kategori)} />
			<button
				class="btn btn-primary btn-sm btn-soft w-full"
				type="button"
				onclick={(e) => confirmation(e)}
			>
				<ConfirmLoader name="create:category">
					<Icon icon="mdi:plus-thick" /> Tambahkan
				</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
