import type { Statistics_Period } from '$lib/types';

export function today_date() {
	// Create a new date object
	const today = new Date();

	// Format the date in Jakarta time zone with Indonesian locale

	return today.toLocaleDateString('id-ID', {
		day: '2-digit',
		month: 'long',
		year: 'numeric',
		timeZone: 'Asia/Jakarta' // Set the time zone to Jakarta
	});
}

export function toISOStringWithTimezone(date: Date) {
	const pad = (n: number) => `${Math.floor(Math.abs(n))}`.padStart(2, '0');
	return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}T${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
}

const now = new Date();
const isoString = toISOStringWithTimezone(now);

export function today() {
	// Create a new date object
	const today = new Date();

	// Format the date using Intl.DateTimeFormat
	const formattedDate = new Intl.DateTimeFormat('en-CA', {
		timeZone: 'Asia/Jakarta', // Specify Jakarta timezone
		year: 'numeric',
		month: '2-digit',
		day: '2-digit'
	}).format(today);

	return formattedDate;
}

export function first_last_date() {
	const jakartaTimeZone = 'Asia/Jakarta';

	// Get the current date in Jakarta timezone
	const today = new Date();

	// Get the first day of the month in Jakarta timezone
	const firstDay = new Date(Date.UTC(today.getFullYear(), today.getMonth(), 1));
	const firstDate = new Intl.DateTimeFormat('en-CA', {
		timeZone: jakartaTimeZone,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit'
	}).format(firstDay);

	// Get the last day of the month in Jakarta timezone
	const lastDay = new Date(Date.UTC(today.getFullYear(), today.getMonth() + 1, 0));
	const lastDate = new Intl.DateTimeFormat('en-CA', {
		timeZone: jakartaTimeZone,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit'
	}).format(lastDay);

	return [firstDate, lastDate];
}

export function current_year() {
	const now = new Date();

	// Format the year in Jakarta timezone
	const formattedYear = new Intl.DateTimeFormat('en-US', {
		timeZone: 'Asia/Jakarta',
		year: 'numeric'
	}).format(now);

	return formattedYear;
}

// Function to convert a number into its Indonesian word equivalent
function numberToIndonesianWords(num: number): string {
	const words: { [key: number]: string } = {
		1: 'satu',
		2: 'dua',
		3: 'tiga',
		4: 'empat',
		5: 'lima',
		6: 'enam',
		7: 'tujuh',
		8: 'delapan',
		9: 'sembilan',
		10: 'sepuluh',
		11: 'sebelas',
		12: 'dua belas',
		13: 'tiga belas',
		14: 'empat belas',
		15: 'lima belas',
		16: 'enam belas',
		17: 'tujuh belas',
		18: 'delapan belas',
		19: 'sembilan belas',
		20: 'dua puluh',
		30: 'tiga puluh',
		40: 'empat puluh',
		50: 'lima puluh',
		60: 'enam puluh',
		70: 'tujuh puluh',
		80: 'delapan puluh',
		90: 'sembilan puluh'
	};

	if (num <= 20) {
		return words[num];
	} else if (num < 100) {
		const tens = Math.floor(num / 10) * 10;
		const ones = num % 10;
		return `${words[tens]} ${words[ones] || ''}`.trim();
	} else {
		return num.toString(); // If it's greater than 99, return the number itself.
	}
}

// Function to calculate the difference between two dates
export function calculateDaysBetweenDates(date1: string, date2: string): string {
	let d1 = new Date(date1);
	let d2 = new Date(date2);

	// Ensure d2 is later than d1
	if (d2 < d1) {
		const temp = d1;
		d1 = d2;
		d2 = temp;
	}

	// Calculate the difference in time
	const timeDiff = d2.getTime() - d1.getTime();

	// Convert time difference to days
	const daysDiff = timeDiff / (1000 * 3600 * 24) + 1;

	// Convert the number of days to Indonesian words
	const daysInWords = numberToIndonesianWords(daysDiff);

	// Return the formatted result
	return `${daysDiff} (${daysInWords}) hari`;
}

export function formatToProperDate(date: string) {
	console.log(date);
	return new Date(date).toLocaleDateString('id-ID', {
		day: '2-digit',
		month: 'long',
		year: 'numeric',
		timeZone: 'Asia/Jakarta' // Set the time zone to Jakarta
	});
}

export const indonesianMonths = [
	'Januari',
	'Februari',
	'Maret',
	'April',
	'Mei',
	'Juni',
	'Juli',
	'Agustus',
	'September',
	'Oktober',
	'November',
	'Desember'
];

export function getDateRange(period: Statistics_Period): [string, string] {
	const today = new Date();
	const todayStr = today.toISOString().split('T')[0]; // Format today's date as YYYY-MM-DD
	let startDate: Date;

	switch (period) {
		case '1day':
			startDate = new Date(today);
			startDate.setDate(today.getDate() - 1);
			break;
		case '1week':
			startDate = new Date(today);
			startDate.setDate(today.getDate() - 7);
			break;
		case '1month':
			startDate = new Date(today);
			startDate.setMonth(today.getMonth() - 1);
			break;
		case '3month':
			startDate = new Date(today);
			startDate.setMonth(today.getMonth() - 3);
			break;
		case '6month':
			startDate = new Date(today);
			startDate.setMonth(today.getMonth() - 6);
			break;
		case 'ytd':
			startDate = new Date(today.getFullYear(), 0, 1); // Start of the current year
			break;
		case 'alltime':
			return ['2024-01-01', todayStr]; // Default start date
		default:
			throw new Error('Invalid period');
	}

	const startDateStr = startDate.toISOString().split('T')[0]; // Format start date as YYYY-MM-DD
	return [startDateStr, todayStr];
}

export function isDateBetween(dateToCheck: string, startDate: string, endDate: string): boolean {
	// Convert strings to Date objects
	const check = new Date(dateToCheck);
	const start = new Date(startDate);
	const end = new Date(endDate);

	// Validate dates
	if (isNaN(check.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
		throw new Error('Invalid date format. Use YYYY-MM-DD');
	}

	// Handle cases where startDate might be after endDate
	const [actualStart, actualEnd] = start <= end ? [start, end] : [end, start];

	// Check if date is between (inclusive)
	return check >= actualStart && check <= actualEnd;
}
