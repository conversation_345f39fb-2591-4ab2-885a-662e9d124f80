<script lang="ts">
	import { onMount, onDestroy } from 'svelte';

	interface IProps {
		start: string;
	}

	const { start }: IProps = $props();

	$inspect(start);

	let now = $state(new Date());
	let interval: number;

	onMount(() => {
		// Update every second
		interval = setInterval(() => {
			now = new Date();
		}, 1000);
	});

	onDestroy(() => {
		if (interval) {
			clearInterval(interval);
		}
	});

	function getTimeElapsed(): string {
		// Simple time difference calculation
		const startDate = new Date(start);
		const currentDate = new Date();

		// Check for invalid dates
		if (isNaN(startDate.getTime())) {
			console.error('Invalid start date:', start);
			return 'Invalid date';
		}

		const diffMs = currentDate.getTime() + 7 * 60 * 60 * 1000 - startDate.getTime(); // Add 7 hours to account for time zone difference

		// Debug logging for negative time
		if (diffMs < 0) {
			console.log('Negative time detected:', {
				start,
				startDate: startDate.toISOString(),
				currentDate: currentDate.toISOString(),
				diffMs,
				startDateLocal: startDate.toString(),
				currentDateLocal: currentDate.toString()
			});
		}

		// Convert to different time units
		const diffSeconds = Math.floor(Math.abs(diffMs) / 1000);
		const diffMinutes = Math.floor(diffSeconds / 60);
		const diffHours = Math.floor(diffMinutes / 60);
		const diffDays = Math.floor(diffHours / 24);

		const prefix = diffMs < 0 ? '⚠️ ' : '';

		if (diffDays > 0) {
			return `${prefix}${diffDays} hari`;
		} else if (diffHours > 0) {
			return `${prefix}${diffHours} jam`;
		} else if (diffMinutes > 0) {
			return `${prefix}${diffMinutes} menit`;
		} else {
			return `${prefix}${diffSeconds} detik`;
		}
	}

	const timeElapsed = $derived(getTimeElapsed());
</script>

{timeElapsed}
