<script lang="ts">
	import { onMount, onDestroy } from 'svelte';

	interface IProps {
		start: string;
	}

	const { start }: IProps = $props();

	let now = $state(new Date());
	let interval: number;

	onMount(() => {
		// Update every second
		interval = setInterval(() => {
			now = new Date();
		}, 1000);
	});

	onDestroy(() => {
		if (interval) {
			clearInterval(interval);
		}
	});

	function getTimeElapsed(now: Date): string {
		const past = new Date(start);
		const diffMs = now.getTime() - past.getTime();

		// Convert to different time units
		const diffSeconds = Math.floor(diffMs / 1000);
		const diffMinutes = Math.floor(diffSeconds / 60);
		const diffHours = Math.floor(diffMinutes / 60);
		const diffDays = Math.floor(diffHours / 24);

		if (diffDays > 0) {
			return `${diffDays} hari${diffDays > 1 ? '' : ''}`;
		} else if (diffHours > 0) {
			return `${diffHours} jam${diffHours > 1 ? '' : ''}`;
		} else if (diffMinutes > 0) {
			return `${diffMinutes} menit${diffMinutes > 1 ? '' : ''}`;
		} else {
			return `${diffSeconds} detik${diffSeconds > 1 ? '' : ''}`;
		}
	}

	const timeElapsed = $derived(getTimeElapsed(now));
</script>

{timeElapsed}
