<script lang="ts">
	import { onMount, onDestroy } from 'svelte';

	interface IProps {
		start: string;
	}

	const { start }: IProps = $props();

	$inspect(start);

	let now = $state(new Date());
	let interval: number;

	onMount(() => {
		// Update every second
		interval = setInterval(() => {
			now = new Date();
		}, 1000);
	});

	onDestroy(() => {
		if (interval) {
			clearInterval(interval);
		}
	});

	function getTimeElapsed(now: Date): string {
		// Simple time difference calculation
		const startDate = new Date(start);

		// Check for invalid dates
		if (isNaN(startDate.getTime())) {
			console.error('Invalid start date:', start);
			return 'Invalid date';
		}

		const diffMs = currentDate.getTime() + 7 * 60 * 60 * 1000 - startDate.getTime(); // Add 7 hours to account for time zone difference

		// Debug logging for negative time
		if (diffMs < 0) {
			console.log('Negative time detected:', {
				start,
				startDate: startDate.toISOString(),
				currentDate: currentDate.toISOString(),
				diffMs,
				startDateLocal: startDate.toString(),
				currentDateLocal: currentDate.toString()
			});
		}

		// Convert to total seconds and format as hh:mm:ss
		const totalSeconds = Math.floor(Math.abs(diffMs) / 1000);
		const prefix = diffMs < 0 ? '⚠️ ' : '';

		// Format as hh:mm:ss
		const hours = Math.floor(totalSeconds / 3600);
		const minutes = Math.floor((totalSeconds % 3600) / 60);
		const seconds = totalSeconds % 60;

		const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

		return `${prefix}${formattedTime}`;
	}

	const timeElapsed = $derived(getTimeElapsed(now));
</script>

{timeElapsed}
