<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import { retrieveAll } from './data.remote';
	import { head } from 'effect/Schema';

	const { data } = $props();

	const allData = retrieveAll();
</script>

<div class="flex justify-around gap-2">
	<a href="/stok-opname/opname" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:plus" /> Buat SO
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<svelte:boundary>
	{#if allData.error}
		<p>oops!</p>
	{:else if allData.loading}
		<p>loading...</p>
	{:else}
		<Table
			table_header={[
				['numbering', 'No.'],
				['id_stock_opname', 'ID Stok Opname'],
				['created_at', 'Tanggal Stok Opname', 'date'],
				['custom', 'User'],
				['custom', 'Actions']
			]}
			table_data={allData.current?.data}
		>
			{#snippet custom({ header, body })}
				{#if header === 'Actions'}
					<div class="flex items-center gap-2">
						<button class="btn btn-sm btn-outline btn-primary uppercase">
							<Icon icon="mdi:open-in-new" />
						</button>
						<button class="btn btn-sm btn-outline btn-primary uppercase">
							<Icon icon="mdi:pencil" />
						</button>
						<button class="btn btn-sm btn-outline btn-primary uppercase">
							<Icon icon="mdi:trash-can" />
						</button>
					</div>
				{:else if header === 'User'}
					{body.karyawan.nama}
				{/if}
			{/snippet}
		</Table>
	{/if}
</svelte:boundary>

<br />

<!-- TEMPORARY because of Remote Function Experiment -->
<!-- 
<div class="flex justify-end">
	<PaginationUsingParam total_content={data.} />
</div> -->
