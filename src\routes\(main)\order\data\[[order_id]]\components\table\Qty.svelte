<script lang="ts">
	import type { <PERSON><PERSON>, Paket, Sparepart } from '$lib/schema/general';

	import { getInvoiceState, type Service } from '../../serviceOrder/InvoiceState.svelte';
	import Currency from '$lib/inputs/Currency.svelte';
	import Icon from '@iconify/svelte';

	interface IProps {
		body: Service[];
		index: number;
	}
	const { body, index: groupIndex }: IProps = $props();
	const kind = $derived(body[0]?.kind);

	const invoiceState = getInvoiceState();

	function removeItem(index: number) {
		invoiceState.service_order[groupIndex].splice(index, 1);
	}
</script>

{#if !invoiceState.editable}
	&nbsp;
	<div class="mb-1"></div>
{/if}

<div class="flex flex-col gap-1">
	<ol class="flex flex-col">
		{#each body as _, i}
			<li class="mb-1 flex items-center gap-2">
				<input
					type="number"
					name="qty"
					id="qty-{kind}-{i}"
					class="input w-14 {kind !== 'paket' ? 'input-xs' : 'input-sm'} {invoiceState.editable
						? ''
						: 'input-ghost '}"
					min="1"
					bind:value={invoiceState.service_order[groupIndex][i].qty}
					readonly={!invoiceState.editable}
				/>
				{#if invoiceState.editable}
					<div class="tooltip" data-tip="Hapus">
						<button class="btn btn-xs btn-outline btn-error" onclick={() => removeItem(i)}>
							<Icon icon="heroicons:trash-16-solid" font-size="1.2em" />
						</button>
					</div>
				{/if}
			</li>
		{/each}
	</ol>

	{#if (kind === 'jasa' || kind === 'sparepart') && invoiceState.editable}
		<p>&nbsp;</p>
	{/if}
</div>
