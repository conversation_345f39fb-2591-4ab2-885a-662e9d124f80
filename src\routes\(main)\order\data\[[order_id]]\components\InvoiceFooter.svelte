<script lang="ts">
	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte.js';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import { getValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte.js';
	import { enhance } from '$app/forms';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import Icon from '@iconify/svelte';

	const invoiceState = getInvoiceState();
	const validationErrorState = getValidationErrorState();
	const confirmState = getConfirmState();
	const toastState = getToastState();

	const submitConfirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `<PERSON><PERSON><PERSON><PERSON>a yakin akan membuat order ini?`,
				loader: 'action:order'
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<footer class="flex h-1/12 items-center justify-end gap-2 p-4">
	{#if invoiceState && invoiceState.editable}
		<form
			action="?/submit:order"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState)}
		>
			<input type="hidden" name="order" value={JSON.stringify(invoiceState.order)} />
			<input
				type="hidden"
				name="service_order"
				value={JSON.stringify(invoiceState.service_order)}
			/>

			<input type="hidden" name="subtotal" value={invoiceState.subtotal} />
			<input
				type="hidden"
				name="discount_by_percentage"
				value={invoiceState.discountByPercentage}
			/>
			<input type="hidden" name="discount_by_value" value={invoiceState.discountByValue} />
			<input type="hidden" name="ppn" value={invoiceState.ppn} />
			<input type="hidden" name="total" value={invoiceState.total} />

			<button class="btn btn-primary" type="button" onclick={(e) => submitConfirmation(e)}>
				<ConfirmLoader name="submit:order">
					<Icon icon="ri:download-2-fill" /> Simpan
				</ConfirmLoader>
			</button>
		</form>
	{/if}
</footer>
