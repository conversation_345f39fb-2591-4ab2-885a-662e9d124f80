<script lang="ts">
	import { untrack } from 'svelte';
	import Icon from '@iconify/svelte';
	import {
		getInvoiceState,
		setInvoiceState,
		type Service
	} from '../../../../order/data/[[order_id]]/serviceOrder/InvoiceState.svelte.js';
	import ServiceOrderTable from '../../../../order/data/[[order_id]]/components/ServiceOrderTable.svelte';

	const { data, params } = $props();

	let invoiceState = $state(getInvoiceState());
	setInvoiceState(data.invoice);

	$effect(() => {
		invoiceState = getInvoiceState();

		untrack(() => {
			invoiceState.order = data.invoice.order;
			invoiceState.editable = false;

			type Services = [Service[], Service[], Service[], Service[]];
			console.log(data.serviceOrder);
			invoiceState.service_order = data.serviceOrder as Services;
		});
	});
</script>

<div class="flex justify-start gap-2">
	<a href="/montir/riwayat/{params.id}" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>
</div>

<br />

<section class="grow overflow-auto">
	<ServiceOrderTable />
</section>
