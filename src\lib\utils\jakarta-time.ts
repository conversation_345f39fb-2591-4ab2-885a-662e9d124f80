/**
 * Utility functions for handling Jakarta timezone (Asia/Jakarta)
 */

const JAKARTA_TIMEZONE = 'Asia/Jakarta';

/**
 * Get current date and time in Jakarta timezone
 * @returns Date object representing current time in Jakarta
 */
export function getJakartaNow(): Date {
	return new Date(new Date().toLocaleString('en-US', { timeZone: JAKARTA_TIMEZONE }));
}

/**
 * Convert any date to Jakarta timezone
 * @param date - Date string or Date object
 * @returns Date object in Jakarta timezone
 */
export function toJakartaTime(date: string | Date): Date {
	const dateObj = typeof date === 'string' ? new Date(date) : date;
	return new Date(dateObj.toLocaleString('en-US', { timeZone: JAKARTA_TIMEZONE }));
}

/**
 * Format time to Jakarta timezone string
 * @param dateString - ISO date string or Date object
 * @param options - Intl.DateTimeFormatOptions (optional)
 * @returns Formatted time string in Jakarta timezone
 */
export function formatJakartaTime(
	dateString: string | Date,
	options?: Intl.DateTimeFormatOptions
): string {
	const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
	const defaultOptions: Intl.DateTimeFormatOptions = {
		timeZone: JAKARTA_TIMEZONE,
		hour: '2-digit',
		minute: '2-digit',
		second: '2-digit',
		hour12: false
	};
	
	return date.toLocaleTimeString('id-ID', { ...defaultOptions, ...options });
}

/**
 * Format date to Jakarta timezone string
 * @param dateString - ISO date string or Date object
 * @param options - Intl.DateTimeFormatOptions (optional)
 * @returns Formatted date string in Jakarta timezone
 */
export function formatJakartaDate(
	dateString: string | Date,
	options?: Intl.DateTimeFormatOptions
): string {
	const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
	const defaultOptions: Intl.DateTimeFormatOptions = {
		timeZone: JAKARTA_TIMEZONE,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit'
	};
	
	return date.toLocaleDateString('id-ID', { ...defaultOptions, ...options });
}

/**
 * Format full date and time to Jakarta timezone string
 * @param dateString - ISO date string or Date object
 * @param options - Intl.DateTimeFormatOptions (optional)
 * @returns Formatted date and time string in Jakarta timezone
 */
export function formatJakartaDateTime(
	dateString: string | Date,
	options?: Intl.DateTimeFormatOptions
): string {
	const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
	const defaultOptions: Intl.DateTimeFormatOptions = {
		timeZone: JAKARTA_TIMEZONE,
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
		second: '2-digit',
		hour12: false
	};
	
	return date.toLocaleString('id-ID', { ...defaultOptions, ...options });
}

/**
 * Calculate time difference between two dates in Jakarta timezone
 * @param startDate - Start date string or Date object
 * @param endDate - End date string or Date object (defaults to current Jakarta time)
 * @returns Time difference in milliseconds
 */
export function getJakartaTimeDiff(
	startDate: string | Date,
	endDate: string | Date = getJakartaNow()
): number {
	const start = toJakartaTime(startDate);
	const end = toJakartaTime(endDate);
	return end.getTime() - start.getTime();
}
